import express from "express";
import { createServer } from "http";
import { Server } from "socket.io";
import cors from "cors";

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: { origin: "*", methods: ["GET", "POST"] },
});

app.use(cors({ origin: "http://localhost:5173" }));

let players = [];
let playerSockets = new Map(); // Map player names to socket IDs
let gameData = {
  state: "waiting",
  message: "Waiting for other player...",
  chosenBox: null,
  player1: null,
  player2: null,
};

io.on("connection", (socket) => {
  console.log("A user connected:", socket.id);

  socket.on("joinGame", (playerName) => {
    if (!players.includes(playerName)) {
      players.push(playerName);
      playerSockets.set(playerName, socket.id);

      // Assign player roles
      if (players.length === 1) {
        gameData.player1 = playerName;
      } else if (players.length === 2) {
        gameData.player2 = playerName;
      }

      console.log(`${playerName} joined. Players:`, players);
      console.log(
        `Player 1: ${gameData.player1}, Player 2: ${gameData.player2}`
      );
    }

    if (players.length === 2) {
      gameData.state = "choosing";
      console.log("Game starting! Emitting gameUpdate:", gameData);

      // Send different messages to each player
      const player1Socket = playerSockets.get(gameData.player1);
      const player2Socket = playerSockets.get(gameData.player2);

      io.to(player1Socket).emit("gameUpdate", {
        ...gameData,
        message: "You are Player 1. Choose a box!",
        playerRole: "player1",
        isYourTurn: true,
      });

      io.to(player2Socket).emit("gameUpdate", {
        ...gameData,
        message: "You are Player 2. Wait for Player 1 to choose a box.",
        playerRole: "player2",
        isYourTurn: false,
      });
    }
  });

  socket.on("selectBox", ({ playerName, box }) => {
    console.log(
      `${playerName} selected box ${box}, current state: ${gameData.state}`
    );

    if (
      gameData.state === "choosing" &&
      !gameData.chosenBox &&
      playerName === gameData.player1
    ) {
      // Only Player 1 can choose a box
      gameData.chosenBox = box;
      gameData.state = "guessing";

      const player1Socket = playerSockets.get(gameData.player1);
      const player2Socket = playerSockets.get(gameData.player2);

      // Send different messages to each player
      io.to(player1Socket).emit("gameUpdate", {
        ...gameData,
        message: "You chose a box! Wait for Player 2 to guess.",
        playerRole: "player1",
        isYourTurn: false,
      });

      io.to(player2Socket).emit("gameUpdate", {
        ...gameData,
        message: "Player 1 has chosen a box. Now guess which one!",
        playerRole: "player2",
        isYourTurn: true,
      });

      // Don't reveal the chosen box to Player 2
      io.to(player1Socket).emit("player1ChosenBox", { chosenBox: box });
    } else if (
      gameData.state === "guessing" &&
      gameData.chosenBox &&
      playerName === gameData.player2
    ) {
      // Only Player 2 can make a guess
      const isCorrect = box === gameData.chosenBox;
      gameData.state = "finished";

      const player1Socket = playerSockets.get(gameData.player1);
      const player2Socket = playerSockets.get(gameData.player2);

      const resultMessage = isCorrect
        ? `Correct! Player 2 guessed ${box} and it was right!`
        : `Wrong! Player 2 guessed ${box} but the answer was ${gameData.chosenBox}`;

      // Send result to both players
      io.to(player1Socket).emit("gameUpdate", {
        ...gameData,
        message: resultMessage,
        playerRole: "player1",
        isYourTurn: false,
      });

      io.to(player2Socket).emit("gameUpdate", {
        ...gameData,
        message: resultMessage,
        playerRole: "player2",
        isYourTurn: false,
      });

      // Reveal the chosen box to Player 2
      io.to(player2Socket).emit("player1ChosenBox", {
        chosenBox: gameData.chosenBox,
      });

      // Reset game after 5 seconds
      setTimeout(() => {
        gameData.state = "choosing";
        gameData.chosenBox = null;

        io.to(player1Socket).emit("gameUpdate", {
          ...gameData,
          message: "New round! You are Player 1. Choose a box!",
          playerRole: "player1",
          isYourTurn: true,
        });

        io.to(player2Socket).emit("gameUpdate", {
          ...gameData,
          message: "New round! You are Player 2. Wait for Player 1 to choose.",
          playerRole: "player2",
          isYourTurn: false,
        });

        // Clear the chosen box for both players
        io.emit("player1ChosenBox", { chosenBox: null });
      }, 5000);
    }
  });

  socket.on("disconnect", () => {
    console.log("A user disconnected:", socket.id);
    // Find and remove the player by socket ID
    for (const [playerName, socketId] of playerSockets.entries()) {
      if (socketId === socket.id) {
        players = players.filter((player) => player !== playerName);
        playerSockets.delete(playerName);
        console.log(`${playerName} disconnected. Remaining players:`, players);
        break;
      }
    }

    // Reset game if not enough players
    if (players.length < 2) {
      gameData = {
        state: "waiting",
        message: "Waiting for other player...",
        chosenBox: null,
        player1: null,
        player2: null,
      };
      io.emit("gameUpdate", gameData);
    }
  });
});

const PORT = 5050;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
