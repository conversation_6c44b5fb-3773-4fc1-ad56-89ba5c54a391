import express from "express";
import { createServer } from "http";
import { Server } from "socket.io";
import cors from "cors";

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: { origin: "*", methods: ["GET", "POST"] },
});

app.use(cors({ origin: "http://localhost:5173" }));

let players = [];
let gameData = {
  state: "waiting",
  message: "Waiting for other player...",
  chosenBox: null,
};

io.on("connection", (socket) => {
  console.log("A user connected:", socket.id);

  socket.on("joinGame", (playerName) => {
    if (!players.includes(playerName)) {
      players.push(playerName);
      console.log(`${playerName} joined. Players:`, players);
    }

    if (players.length === 2) {
      gameData.state = "choosing";
      gameData.message = "Player 1, choose a box!";
      io.emit("gameUpdate", gameData);
    }
  });

  socket.on("selectBox", ({ playerName, box }) => {
    if (gameData.state === "choosing" && !gameData.chosenBox) {
      gameData.chosenBox = box;
      gameData.state = "guessing";
      gameData.message = "Player 2, guess the selected box!";
      io.emit("player1ChosenBox", { chosenBox: box });
      io.emit("gameUpdate", gameData);
    }
  });

  socket.on("disconnect", () => {
    console.log("A user disconnected:", socket.id);
    players = players.filter((player) => player !== socket.id);
  });
});

const PORT = 5050;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
