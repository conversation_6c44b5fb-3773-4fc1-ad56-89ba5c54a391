import express from "express";
import { createServer } from "http";
import { Server } from "socket.io";
import cors from "cors";

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: { origin: "*", methods: ["GET", "POST"] },
});

app.use(cors({ origin: "http://localhost:5173" }));

let players = [];
let playerSockets = new Map(); // Map player names to socket IDs
let gameData = {
  state: "waiting",
  message: "Waiting for other player...",
  chosenBox: null,
};

io.on("connection", (socket) => {
  console.log("A user connected:", socket.id);

  socket.on("joinGame", (playerName) => {
    if (!players.includes(playerName)) {
      players.push(playerName);
      playerSockets.set(playerName, socket.id);
      console.log(`${playerName} joined. Players:`, players);
    }

    if (players.length === 2) {
      gameData.state = "choosing";
      gameData.message = "Player 1, choose a box!";
      console.log("Game starting! Emitting gameUpdate:", gameData);
      io.emit("gameUpdate", gameData);
    }
  });

  socket.on("selectBox", ({ playerName, box }) => {
    console.log(
      `${playerName} selected box ${box}, current state: ${gameData.state}`
    );

    if (gameData.state === "choosing" && !gameData.chosenBox) {
      // Player 1 chooses a box
      gameData.chosenBox = box;
      gameData.state = "guessing";
      gameData.message = "Player 2, guess the selected box!";
      io.emit("player1ChosenBox", { chosenBox: box });
      io.emit("gameUpdate", gameData);
    } else if (gameData.state === "guessing" && gameData.chosenBox) {
      // Player 2 makes a guess
      const isCorrect = box === gameData.chosenBox;
      gameData.state = "finished";
      gameData.message = isCorrect
        ? `Correct! Player 2 guessed ${box} and it was right!`
        : `Wrong! Player 2 guessed ${box} but the answer was ${gameData.chosenBox}`;
      io.emit("gameUpdate", gameData);

      // Reset game after 5 seconds
      setTimeout(() => {
        gameData = {
          state: "choosing",
          message: "Player 1, choose a box!",
          chosenBox: null,
        };
        io.emit("gameUpdate", gameData);
      }, 5000);
    }
  });

  socket.on("disconnect", () => {
    console.log("A user disconnected:", socket.id);
    // Find and remove the player by socket ID
    for (const [playerName, socketId] of playerSockets.entries()) {
      if (socketId === socket.id) {
        players = players.filter((player) => player !== playerName);
        playerSockets.delete(playerName);
        console.log(`${playerName} disconnected. Remaining players:`, players);
        break;
      }
    }

    // Reset game if not enough players
    if (players.length < 2) {
      gameData = {
        state: "waiting",
        message: "Waiting for other player...",
        chosenBox: null,
      };
      io.emit("gameUpdate", gameData);
    }
  });
});

const PORT = 5050;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
