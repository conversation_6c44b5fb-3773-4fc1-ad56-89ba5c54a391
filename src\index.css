/* index.css */
body {
  font-family: Arial, sans-serif;
  background-color: #f4f4f4;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  margin: 0;
}

.container {
  text-align: center;
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.box-grid {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 20px;
}

.box {
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ddd;
  border: 2px solid black;
  cursor: pointer;
  font-size: 20px;
  font-weight: bold;
  transition: background 0.3s;
}

.box.selected {
  background: blue;
  color: white;
}

input {
  padding: 8px;
  margin: 10px;
  border-radius: 5px;
  border: 1px solid gray;
}

button {
  padding: 10px 15px;
  margin-top: 10px;
  border: none;
  background: #007bff;
  color: white;
  cursor: pointer;
  border-radius: 5px;
}

button:hover {
  background: #0056b3;
}
