import { useState, useEffect } from "react";
import io from "socket.io-client";
import Lobby from "./Components/Lobby";
import Game from "./Components/Game";

const socket = io("http://localhost:5050", {
  transports: ["websocket", "polling"],
});

function App() {
  const [playerName, setPlayerName] = useState("");
  const [gameStarted, setGameStarted] = useState(false);

  useEffect(() => {
    socket.on("gameUpdate", ({ state }) => {
      if (state === "choosing") setGameStarted(true);
    });

    return () => {
      socket.off("gameUpdate");
    };
  }, []);

  return (
    <div className="App">
      {!gameStarted ? (
        <Lobby socket={socket} setPlayerName={setPlayerName} />
      ) : (
        <Game socket={socket} playerName={playerName} />
      )}
    </div>
  );
}

export default App;
