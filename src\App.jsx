import { useState, useEffect } from "react";
import io from "socket.io-client";
import Lobby from "./Components/Lobby";
import Game from "./Components/Game";

const socket = io("http://localhost:5050", {
  transports: ["websocket", "polling"],
});

function App() {
  const [playerName, setPlayerName] = useState("");
  const [gameStarted, setGameStarted] = useState(false);

  useEffect(() => {
    socket.on("connect", () => {
      console.log("Socket connected:", socket.id);
    });

    socket.on("gameUpdate", ({ state }) => {
      console.log("App received gameUpdate:", { state });
      if (
        state === "choosing" ||
        state === "guessing" ||
        state === "finished"
      ) {
        setGameStarted(true);
      } else if (state === "waiting") {
        setGameStarted(false);
      }
    });

    socket.on("disconnect", () => {
      console.log("Socket disconnected");
    });

    return () => {
      socket.off("connect");
      socket.off("gameUpdate");
      socket.off("disconnect");
    };
  }, []);

  return (
    <div className="App">
      {!gameStarted ? (
        <Lobby socket={socket} setPlayerName={setPlayerName} />
      ) : (
        <Game socket={socket} playerName={playerName} />
      )}
    </div>
  );
}

export default App;
