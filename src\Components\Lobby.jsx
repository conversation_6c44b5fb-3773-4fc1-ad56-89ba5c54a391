import { useState } from "react";

const Lobby = ({ socket, setPlayerName }) => {
  const [name, setName] = useState("");

  const handleJoin = () => {
    if (name.trim()) {
      setPlayerName(name);
      socket.emit("joinGame", name);
    }
  };

  return (
    <div className="container">
      <h2>Join the Game</h2>
      <input
        type="text"
        placeholder="Enter your name"
        value={name}
        onChange={(e) => setName(e.target.value)}
      />
      <button onClick={handleJoin}>Join Game</button>
    </div>
  );
};

export default Lobby;
