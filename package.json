{"name": "box-guessing-game", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"cors": "^2.8.5", "express": "^4.21.2", "react": "^19.0.0", "react-dom": "^19.0.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwindcss": "^4.0.13", "ws": "^8.18.1"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}