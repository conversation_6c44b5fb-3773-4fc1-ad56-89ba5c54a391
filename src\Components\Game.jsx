import { useState, useEffect } from "react";
import BoxGrid from "./BoxGrid";

const Game = ({ playerName, socket }) => {
  const [selectedBox, setSelectedBox] = useState(null);
  const [message, setMessage] = useState("Waiting for other player...");
  const [gameState, setGameState] = useState("waiting");

  useEffect(() => {
    socket.on("gameUpdate", ({ state, message }) => {
      console.log("Game received gameUpdate:", { state, message });
      setGameState(state);
      setMessage(message);
    });

    socket.on("player1ChosenBox", ({ chosenBox }) => {
      console.log("Game received player1ChosenBox:", { chosenBox });
      setSelectedBox(chosenBox);
    });

    return () => {
      socket.off("gameUpdate");
      socket.off("player1ChosenBox");
    };
  }, [socket]);

  const handleSelectBox = (box) => {
    if (gameState === "choosing" || gameState === "guessing") {
      if (gameState === "choosing") {
        setSelectedBox(box);
      }
      socket.emit("selectBox", { playerName, box });
    }
  };

  return (
    <div className="container">
      <h2>Game</h2>
      <p>{message}</p>
      <BoxGrid
        onSelect={handleSelectBox}
        selectedBox={selectedBox}
        gameState={gameState}
      />
    </div>
  );
};

export default Game;
