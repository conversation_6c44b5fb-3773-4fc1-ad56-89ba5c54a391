import { useState, useEffect } from "react";
import BoxGrid from "./BoxGrid";

const Game = ({ playerName, socket }) => {
  const [selectedBox, setSelectedBox] = useState(null);
  const [message, setMessage] = useState("Waiting for other player...");
  const [gameState, setGameState] = useState("waiting");
  const [playerRole, setPlayerRole] = useState(null);
  const [isMyTurn, setIsMyTurn] = useState(false);

  useEffect(() => {
    socket.on("gameUpdate", ({ state, message, playerRole, isYourTurn }) => {
      console.log("Game received gameUpdate:", {
        state,
        message,
        playerRole,
        isYourTurn,
      });
      setGameState(state);
      setMessage(message);
      setPlayerRole(playerRole);
      setIsMyTurn(isYourTurn);
    });

    socket.on("player1ChosenBox", ({ chosenBox }) => {
      console.log("Game received player1ChosenBox:", { chosenBox });
      setSelectedBox(chosenBox);
    });

    return () => {
      socket.off("gameUpdate");
      socket.off("player1ChosenBox");
    };
  }, [socket]);

  const handleSelectBox = (box) => {
    // Only allow interaction if it's the player's turn
    if (isMyTurn && (gameState === "choosing" || gameState === "guessing")) {
      if (gameState === "choosing") {
        setSelectedBox(box);
      }
      socket.emit("selectBox", { playerName, box });
    }
  };

  return (
    <div className="container">
      <h2>Game</h2>
      <p>
        <strong>
          You are {playerRole === "player1" ? "Player 1" : "Player 2"}
        </strong>
      </p>
      <p>{message}</p>
      <p style={{ color: isMyTurn ? "green" : "red" }}>
        {isMyTurn ? "Your turn!" : "Wait for your turn"}
      </p>
      <BoxGrid
        onSelect={handleSelectBox}
        selectedBox={selectedBox}
        gameState={gameState}
        isMyTurn={isMyTurn}
      />
    </div>
  );
};

export default Game;
