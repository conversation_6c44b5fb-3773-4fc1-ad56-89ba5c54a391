const BoxGrid = ({ onSelect, selectedBox }) => {
  const boxes = ["A", "B", "C", "D", "E"];

  return (
    <div className="box-grid">
      {boxes.map((box) => (
        <button
          key={box}
          onClick={() => onSelect(box)}
          disabled={selectedBox !== null}
          className={selectedBox === box ? "selected" : ""}
        >
          {box}
        </button>
      ))}
    </div>
  );
};

export default BoxGrid;
