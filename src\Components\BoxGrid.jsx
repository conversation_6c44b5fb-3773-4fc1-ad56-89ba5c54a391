const BoxGrid = ({ onSelect, selectedBox, gameState, isMyTurn }) => {
  const boxes = ["A", "B", "C", "D", "E"];

  return (
    <div className="box-grid">
      {boxes.map((box) => (
        <button
          key={box}
          onClick={() => onSelect(box)}
          disabled={
            gameState === "finished" || gameState === "waiting" || !isMyTurn
          }
          className={`box ${selectedBox === box ? "selected" : ""} ${
            !isMyTurn ? "disabled" : ""
          }`}
        >
          {box}
        </button>
      ))}
    </div>
  );
};

export default BoxGrid;
