const BoxGrid = ({ onSelect, selectedBox, gameState }) => {
  const boxes = ["A", "B", "C", "D", "E"];

  return (
    <div className="box-grid">
      {boxes.map((box) => (
        <button
          key={box}
          onClick={() => onSelect(box)}
          disabled={gameState === "finished" || gameState === "waiting"}
          className={`box ${selectedBox === box ? "selected" : ""}`}
        >
          {box}
        </button>
      ))}
    </div>
  );
};

export default BoxGrid;
