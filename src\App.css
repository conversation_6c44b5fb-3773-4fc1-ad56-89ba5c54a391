.container {
  max-width: 800px;
  margin: auto;
  text-align: center;
  padding: 20px;
}

.box {
  width: 80px;
  height: 80px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 2px solid black;
  margin: 10px;
  cursor: pointer;
  font-size: 1.5rem;
}

.box.selected {
  background-color: blue;
  color: white;
}

.button {
  margin-top: 10px;
  padding: 10px 15px;
  background-color: blue;
  color: white;
  border: none;
  cursor: pointer;
}

.button:hover {
  background-color: darkblue;
}
